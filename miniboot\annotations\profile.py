#!/usr/bin/env python
"""
* @author: cz
* @description: Profile 注解实现

实现 @Profile 注解,用于基于环境配置的条件化 Bean 创建.
支持多环境配置和复杂的 Profile 表达式.
"""

from typing import Any, Callable, Union, Optional
from .metadata import ProfileMetadata


def Profile(  # noqa: N802
    *profiles: str, expression: Optional[str] = None
) -> Callable[[Union[type, Callable[..., Any]]], Union[type, Callable[..., Any]]]:
    """Profile 条件装配注解装饰器

    根据当前激活的 Profile 决定是否创建 Bean.
    支持多个 Profile 和复杂的 Profile 表达式.

    Args:
        *profiles: Profile 名称列表,支持多个 Profile
        expression: Profile 表达式,支持逻辑运算符(&, |, !)

    Returns:
        装饰器函数

    Examples:
        # 单个 Profile
        @Profile("dev")
        class DevService:
            pass

        # 多个 Profile(OR 关系)
        @Profile("dev", "test")
        class DevTestService:
            pass

        # Profile 表达式
        @Profile(expression="dev & !prod")
        class DevNotProdService:
            pass

        # 复杂表达式
        @Profile(expression="(dev | test) & !prod")
        class NonProdService:
            pass
    """

    def decorator(target: Union[type, Callable[..., Any]]) -> Union[type, Callable[..., Any]]:
        # 创建 Profile 元数据
        metadata = ProfileMetadata(profiles=list(profiles), expression=expression)

        # 存储元数据
        target.__profile_metadata__ = metadata  # type: ignore[union-attr]
        target.__is_profile__ = True  # type: ignore[union-attr]
        target.__profile_names__ = list(profiles)  # type: ignore[union-attr]
        target.__profile_expression__ = expression  # type: ignore[union-attr]

        return target

    return decorator


def is_profile(target: Any) -> bool:
    """检查目标是否有 @Profile 注解

    Args:
        target: 要检查的目标(类或方法)

    Returns:
        True 表示有 @Profile 注解
    """
    return hasattr(target, "__is_profile__") and target.__is_profile__


def get_profile_metadata(target: Any) -> Optional[ProfileMetadata]:
    """获取 @Profile 注解的元数据

    Args:
        target: 目标对象

    Returns:
        Profile 元数据,如果没有则返回 None
    """
    return getattr(target, "__profile_metadata__", None)


def get_profile_names(target: Any) -> list[str]:
    """获取 @Profile 注解的 Profile 名称列表

    Args:
        target: 目标对象

    Returns:
        Profile 名称列表
    """
    return getattr(target, "__profile_names__", [])


def get_profile_expression(target: Any) -> Optional[str]:
    """获取 @Profile 注解的表达式

    Args:
        target: 目标对象

    Returns:
        Profile 表达式,如果没有则返回 None
    """
    return getattr(target, "__profile_expression__", None)


class ProfileCondition:
    """Profile 条件类

    用于评估 @Profile 注解的条件是否满足.
    """

    def matches(self, context: Any) -> bool:
        """检查 Profile 条件是否匹配

        Args:
            context: 条件上下文,包含环境信息

        Returns:
            True 表示条件匹配
        """
        # 获取当前激活的 Profile
        active_profiles = set()
        if hasattr(context, "environment") and context.environment:
            active_profiles = set(context.environment.get_active_profiles())

        # 如果没有激活的 Profile,使用默认 Profile
        if not active_profiles and hasattr(context, "environment") and context.environment:
            active_profiles = set(context.environment.get_default_profiles())

        # 获取目标的 Profile 元数据
        target = getattr(context, "target", None)
        if not target or not is_profile(target):
            return True

        metadata = get_profile_metadata(target)
        if not metadata:
            return True

        # 如果有表达式,使用表达式评估
        if metadata.expression:
            return self._evaluate_expression(metadata.expression, active_profiles)

        # 如果有 Profile 列表,检查是否有任何一个匹配(OR 关系)
        if metadata.profiles:
            return any(profile in active_profiles for profile in metadata.profiles)

        # 没有指定 Profile,默认匹配
        return True

    def _evaluate_expression(self, expression: str, active_profiles: set[str]) -> bool:
        """评估 Profile 表达式

        Args:
            expression: Profile 表达式
            active_profiles: 当前激活的 Profile 集合

        Returns:
            表达式评估结果
        """
        try:
            # 简化的表达式解析器
            # 支持的操作符:& (AND), | (OR), ! (NOT), () (分组)

            # 预处理:替换 Profile 名称为布尔值
            processed_expr = expression

            # 提取所有 Profile 名称(简单的正则匹配)
            import re

            profile_names = re.findall(r"\b[a-zA-Z_][a-zA-Z0-9_-]*\b", expression)

            # 替换 Profile 名称为布尔值
            for profile_name in profile_names:
                if profile_name not in ["and", "or", "not", "True", "False"]:
                    is_active = profile_name in active_profiles
                    processed_expr = processed_expr.replace(profile_name, str(is_active))

            # 替换操作符
            processed_expr = processed_expr.replace("&", " and ")
            processed_expr = processed_expr.replace("|", " or ")
            processed_expr = processed_expr.replace("!", " not ")

            # 安全评估表达式
            try:
                result = eval(processed_expr, {"__builtins__": {}}, {})
                return bool(result)
            except Exception:
                # 表达式评估失败,默认不匹配
                return False

        except Exception:
            # 表达式解析失败,默认不匹配
            return False


class ProfileEvaluator:
    """Profile 评估器

    负责评估 @Profile 注解的条件是否满足.
    """

    def __init__(self, environment: Any = None) -> None:
        """初始化 Profile 评估器

        Args:
            environment: 环境配置实例
        """
        self.environment = environment
        self.condition = ProfileCondition()

    def evaluate(self, target: Union[type, Callable[..., Any]]) -> bool:
        """评估目标是否满足 Profile 条件

        Args:
            target: 要评估的类或方法

        Returns:
            如果满足条件返回 True,否则返回 False
        """
        if not is_profile(target):
            return True

        # 创建评估上下文
        context = type("Context", (), {"environment": self.environment, "target": target})()

        return self.condition.matches(context)

    def accepts_profiles(self, *profiles: str) -> bool:
        """检查环境是否接受指定的 Profile

        Args:
            *profiles: Profile 名称列表

        Returns:
            如果环境接受任一指定 Profile 返回 True
        """
        if not self.environment:
            return True

        return bool(self.environment.accepts_profiles(*profiles))

    def get_active_profiles(self) -> list[str]:
        """获取当前激活的 Profile 列表

        Returns:
            激活的 Profile 名称列表
        """
        if not self.environment:
            return []

        return list(self.environment.get_active_profiles())

    def get_default_profiles(self) -> list[str]:
        """获取默认的 Profile 列表

        Returns:
            默认的 Profile 名称列表
        """
        if not self.environment:
            return ["default"]

        return list(self.environment.get_default_profiles())


# 便利函数
def create_profile_evaluator(environment=None) -> ProfileEvaluator:
    """创建 Profile 评估器

    Args:
        environment: 环境配置实例

    Returns:
        Profile 评估器实例
    """
    return ProfileEvaluator(environment)


def evaluate_profile(target: Union[type, Callable], environment=None) -> bool:
    """评估目标的 Profile 条件

    Args:
        target: 要评估的目标
        environment: 环境配置实例

    Returns:
        评估结果
    """
    evaluator = create_profile_evaluator(environment)
    return evaluator.evaluate(target)
