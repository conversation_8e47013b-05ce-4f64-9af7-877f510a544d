#!/usr/bin/env python
"""
* @author: cz
* @description: 组件扫描器

实现组件扫描功能,扫描指定包路径下的所有模块,发现带有注解的类和方法.
支持过滤机制、缓存优化和增量扫描.
"""

import importlib
import inspect
import pkgutil
from dataclasses import dataclass, field
from typing import Any, Callable, Optional, Union

from .config import get_component_scan_metadata, is_component_scan
from .base import (is_bean, is_component, is_configuration, is_repository,
                   is_service)
from .event import is_event_listener
from .inject import is_autowired
from .lifecycle import is_post_construct, is_pre_destroy
# 移除复杂缓存依赖，使用简单内存缓存
from .schedule import is_async, is_scheduled


@dataclass
class ScannerConfig:
    """扫描器总配置"""
    # 扫描配置
    enable_lazy_loading: bool = True
    max_cache_size: int = 1000


class ScanFilter:
    """扫描过滤器"""

    def __init__(self):
        self.include_patterns: list[str] = []
        self.exclude_patterns: list[str] = []
        self.include_annotations: set[str] = set()
        self.exclude_annotations: set[str] = set()

    def add_include_pattern(self, pattern: str) -> "ScanFilter":
        """添加包含模式"""
        self.include_patterns.append(pattern)
        return self

    def add_exclude_pattern(self, pattern: str) -> "ScanFilter":
        """添加排除模式"""
        self.exclude_patterns.append(pattern)
        return self

    def add_include_annotation(self, annotation: str) -> "ScanFilter":
        """添加包含注解"""
        self.include_annotations.add(annotation)
        return self

    def add_exclude_annotation(self, annotation: str) -> "ScanFilter":
        """添加排除注解"""
        self.exclude_annotations.add(annotation)
        return self

    def should_include_module(self, module_name: str) -> bool:
        """检查是否应该包含模块"""
        # 检查排除模式
        for pattern in self.exclude_patterns:
            if pattern in module_name:
                return False

        # 如果有包含模式,检查是否匹配
        if self.include_patterns:
            return any(pattern in module_name for pattern in self.include_patterns)

        return True

    def should_include_class(self, _cls: type, annotations: set[str]) -> bool:
        """检查是否应该包含类"""
        # 检查排除注解
        if self.exclude_annotations & annotations:
            return False

        # 如果有包含注解,检查是否匹配
        if self.include_annotations:
            return bool(self.include_annotations & annotations)

        return True


class ScanResult:
    """扫描结果"""

    def __init__(self):
        self.components: dict[str, type] = {}  # 组件类
        self.configurations: dict[str, type] = {}  # 配置类
        self.bean_methods: dict[str, Callable] = {}  # Bean方法
        self.autowired_fields: dict[str, list[str]] = {}  # 自动装配字段
        self.autowired_methods: dict[str, list[str]] = {}  # 自动装配方法
        self.lifecycle_methods: dict[str, dict[str, list[str]]] = {}  # 生命周期方法
        self.async_methods: dict[str, list[str]] = {}  # 异步方法
        self.scheduled_methods: dict[str, list[str]] = {}  # 调度方法
        self.event_listeners: dict[str, list[str]] = {}  # 事件监听器
        self.scanned_modules: set[str] = set()  # 已扫描模块
        self.scan_errors: list[str] = []  # 扫描错误
        self.config_class: Optional[str] = None  # 配置类名称
        self.scan_metadata: Optional[Any] = None  # 扫描元数据
        self.project_root: Optional[str] = None  # 项目根包
        self.included_dependencies: bool = False  # 是否包含依赖

        # 性能监控
        self.scan_time: float = 0.0  # 扫描耗时（秒）
        self.cache_hits: int = 0  # 缓存命中次数
        self.cache_misses: int = 0  # 缓存未命中次数

    def add_component(self, name: str, cls: type):
        """添加组件"""
        self.components[name] = cls

    def add_configuration(self, name: str, cls: type):
        """添加配置类"""
        self.configurations[name] = cls

    def add_bean_method(self, name: str, method: Callable):
        """添加Bean方法"""
        self.bean_methods[name] = method

    def add_autowired_field(self, class_name: str, field_name: str):
        """添加自动装配字段"""
        if class_name not in self.autowired_fields:
            self.autowired_fields[class_name] = []
        self.autowired_fields[class_name].append(field_name)

    def add_autowired_method(self, class_name: str, method_name: str):
        """添加自动装配方法"""
        if class_name not in self.autowired_methods:
            self.autowired_methods[class_name] = []
        self.autowired_methods[class_name].append(method_name)

    def add_lifecycle_method(self, class_name: str, lifecycle_type: str, method_name: str):
        """添加生命周期方法"""
        if class_name not in self.lifecycle_methods:
            self.lifecycle_methods[class_name] = {}
        if lifecycle_type not in self.lifecycle_methods[class_name]:
            self.lifecycle_methods[class_name][lifecycle_type] = []
        self.lifecycle_methods[class_name][lifecycle_type].append(method_name)

    def add_async_method(self, class_name: str, method_name: str):
        """添加异步方法"""
        if class_name not in self.async_methods:
            self.async_methods[class_name] = []
        self.async_methods[class_name].append(method_name)

    def add_scheduled_method(self, class_name: str, method_name: str):
        """添加调度方法"""
        if class_name not in self.scheduled_methods:
            self.scheduled_methods[class_name] = []
        self.scheduled_methods[class_name].append(method_name)

    def add_event_listener(self, class_name: str, method_name: str):
        """添加事件监听器"""
        if class_name not in self.event_listeners:
            self.event_listeners[class_name] = []
        self.event_listeners[class_name].append(method_name)

    def add_error(self, error: str):
        """添加扫描错误"""
        self.scan_errors.append(error)

    def get_total_components(self) -> int:
        """获取组件总数"""
        return len(self.components) + len(self.configurations)

    def get_total_methods(self) -> int:
        """获取方法总数"""
        total = len(self.bean_methods)
        total += sum(len(methods) for methods in self.autowired_methods.values())
        total += sum(len(lifecycle_methods) for class_methods in self.lifecycle_methods.values() for lifecycle_methods in class_methods.values())
        total += sum(len(methods) for methods in self.async_methods.values())
        total += sum(len(methods) for methods in self.scheduled_methods.values())
        total += sum(len(methods) for methods in self.event_listeners.values())
        return total


class ComponentScanner:
    """组件扫描器（高性能版本）"""

    def __init__(self, config: Optional[ScannerConfig] = None):
        # 配置管理
        self.config = config or ScannerConfig()
        self.scan_filter = ScanFilter()

        # 简单内存缓存
        self._scan_cache: dict[str, ScanResult] = {}
        self._module_cache: dict[str, Any] = {}  # 模块缓存
        self._class_cache: dict[str, set[type]] = {}  # 类缓存

        # 基础配置
        self.cache_enabled = True
        self.enable_lazy_loading = self.config.enable_lazy_loading
        self.max_cache_size = self.config.max_cache_size

    def set_filter(self, scan_filter: ScanFilter) -> "ComponentScanner":
        """设置扫描过滤器"""
        self.scan_filter = scan_filter
        return self

    def enable_cache(self, enabled: bool = True) -> "ComponentScanner":
        """启用/禁用缓存"""
        self.cache_enabled = enabled
        if not enabled:
            self._scan_cache.clear()
        return self

    def clear_cache(self) -> "ComponentScanner":
        """清空缓存"""
        self._scan_cache.clear()
        self._module_cache.clear()
        self._class_cache.clear()
        return self

    def set_cache_size(self, size: int) -> "ComponentScanner":
        """设置缓存大小"""
        self.max_cache_size = size
        self._cleanup_cache()
        return self

    def set_lazy_loading(self, enabled: bool = True) -> "ComponentScanner":
        """启用/禁用延迟加载"""
        self.enable_lazy_loading = enabled
        return self

    def _cleanup_cache(self):
        """清理缓存,保持在最大大小限制内"""
        # 清理扫描结果缓存
        if len(self._scan_cache) > self.max_cache_size:
            items_to_remove = len(self._scan_cache) - self.max_cache_size // 2
            keys_to_remove = list(self._scan_cache.keys())[:items_to_remove]
            for key in keys_to_remove:
                self._scan_cache.pop(key, None)

        # 清理模块缓存
        if len(self._module_cache) > self.max_cache_size:
            items_to_remove = len(self._module_cache) - self.max_cache_size // 2
            keys_to_remove = list(self._module_cache.keys())[:items_to_remove]
            for key in keys_to_remove:
                self._module_cache.pop(key, None)

        # 清理类缓存
        if len(self._class_cache) > self.max_cache_size:
            items_to_remove = len(self._class_cache) - self.max_cache_size // 2
            keys_to_remove = list(self._class_cache.keys())[:items_to_remove]
            for key in keys_to_remove:
                self._class_cache.pop(key, None)

    def _generate_cache_key(self, base_packages: list[str]) -> str:
        """生成缓存键"""
        # 包含包列表、过滤器配置和扫描选项
        key_parts = [
            "|".join(sorted(base_packages)),
            f"lazy:{self.enable_lazy_loading}",
            f"inc_patterns:{','.join(self.scan_filter.include_patterns)}",
            f"exc_patterns:{','.join(self.scan_filter.exclude_patterns)}",
            f"inc_annotations:{','.join(sorted(self.scan_filter.include_annotations))}",
            f"exc_annotations:{','.join(sorted(self.scan_filter.exclude_annotations))}"
        ]
        return "|".join(key_parts)

    def scan(self, base_packages: Union[str, list[str]]) -> ScanResult:
        """扫描指定包（高性能版本）"""
        if isinstance(base_packages, str):
            base_packages = [base_packages]

        # 生成缓存键（包含配置信息以确保缓存有效性）
        cache_key = self._generate_cache_key(base_packages)

        # 检查简单缓存
        if self.cache_enabled and cache_key in self._scan_cache:
            return self._scan_cache[cache_key]

        # 执行扫描
        result = ScanResult()

        # 记录扫描开始时间（用于性能监控）
        import time
        start_time = time.time()

        for package in base_packages:
            try:
                self._scan_package(package, result)
            except Exception as e:
                result.add_error(f"扫描包 {package} 时出错: {str(e)}")

        # 记录扫描时间
        scan_time = time.time() - start_time
        result.scan_time = scan_time

        # 存储到简单缓存
        if self.cache_enabled:
            self._scan_cache[cache_key] = result
            self._cleanup_cache()  # 保持缓存大小限制

        return result

    def scan_from_config(self, config_class: type) -> ScanResult:
        """从配置类扫描组件

        从带有@ComponentScan注解的配置类中获取扫描配置,然后执行扫描.

        Args:
            config_class: 带有@ComponentScan注解的配置类

        Returns:
            扫描结果

        Raises:
            ValueError: 如果配置类没有@ComponentScan注解

        Example:
            @Configuration
            @ComponentScan(base_packages=["com.example.service"])
            class AppConfig:
                pass

            scanner = ComponentScanner()
            result = scanner.scan_from_config(AppConfig)
        """
        if not is_component_scan(config_class):
            raise ValueError(f"类 {config_class.__name__} 没有@ComponentScan注解")

        # 获取扫描元数据
        metadata = get_component_scan_metadata(config_class)
        if not metadata:
            raise ValueError(f"无法获取类 {config_class.__name__} 的@ComponentScan元数据")

        # 创建扫描过滤器
        scan_filter = ScanFilter()

        # 添加包含过滤器
        for pattern in metadata.include_filters:
            scan_filter.add_include_pattern(pattern)

        # 添加排除过滤器
        for pattern in metadata.exclude_filters:
            scan_filter.add_exclude_pattern(pattern)

        # 设置过滤器
        original_filter = self.scan_filter
        self.set_filter(scan_filter)

        try:
            # 执行扫描
            result = self.scan(metadata.base_packages)

            # 在扫描结果中记录配置信息
            result.config_class = config_class.__name__
            result.scan_metadata = metadata

            return result
        finally:
            # 恢复原始过滤器
            self.set_filter(original_filter)

    def auto_scan(self, entry_class: type) -> ScanResult:
        """自动扫描

        自动检测入口类的扫描配置并执行扫描.
        类似Spring Boot的@SpringBootApplication,会自动扫描主类所在的包及其子包.

        Args:
            entry_class: 应用入口类或配置类

        Returns:
            扫描结果

        Example:
            # 在maxbot/application.py中
            @MiniBootApplication
            class Application:
                pass

            # 会自动扫描maxbot包及其子包
            scanner = ComponentScanner()
            result = scanner.auto_scan(Application)
        """
        # 检查是否有@ComponentScan注解
        if is_component_scan(entry_class):
            return self.scan_from_config(entry_class)

        # 使用默认扫描策略:扫描入口类所在的包及其子包
        module_name = entry_class.__module__

        # 获取包路径
        # 如果是 maxbot.application,扫描 maxbot;如果是 application,扫描当前模块
        base_package = module_name.split(".")[0] if "." in module_name else module_name

        print(f"自动扫描包: {base_package} (基于入口类: {entry_class.__name__})")

        return self.scan([base_package])

    def auto_scan_project(self, entry_class: type, include_dependencies: bool = True) -> ScanResult:
        """自动扫描整个项目

        类似Spring Boot的自动扫描机制,扫描项目根目录及其依赖.

        Args:
            entry_class: 应用入口类
            include_dependencies: 是否包含依赖包的扫描

        Returns:
            扫描结果

        Example:
            # 在maxbot项目中
            @MiniBootApplication
            class MaxbotApplication:
                pass

            # 自动扫描maxbot项目及miniboot依赖
            result = scanner.auto_scan_project(MaxbotApplication)
        """
        # 获取项目根包
        module_name = entry_class.__module__
        project_root = self._get_project_root(module_name)

        # 要扫描的包列表
        scan_packages = [project_root]

        # 如果包含依赖,添加miniboot包
        if include_dependencies:
            scan_packages.append("miniboot")

        # 检查是否有@ComponentScan注解
        if is_component_scan(entry_class):
            metadata = get_component_scan_metadata(entry_class)
            if metadata and metadata.base_packages:
                # 合并配置的包和自动发现的包
                scan_packages.extend(metadata.base_packages)
                # 去重
                scan_packages = list(set(scan_packages))

        # 创建扫描过滤器,排除测试和临时文件
        scan_filter = ScanFilter()
        scan_filter.add_exclude_pattern("test")
        scan_filter.add_exclude_pattern("tests")
        scan_filter.add_exclude_pattern("__pycache__")
        scan_filter.add_exclude_pattern(".git")
        scan_filter.add_exclude_pattern(".venv")
        scan_filter.add_exclude_pattern("venv")

        # 设置过滤器
        original_filter = self.scan_filter
        self.set_filter(scan_filter)

        try:
            result = self.scan(scan_packages)
            result.project_root = project_root
            result.included_dependencies = include_dependencies
            return result
        finally:
            self.set_filter(original_filter)

    def _get_project_root(self, module_name: str) -> str:
        """获取项目根包名

        Args:
            module_name: 模块名,如 'maxbot.application'

        Returns:
            项目根包名,如 'maxbot'
        """
        # 分割模块名
        parts = module_name.split(".")

        # 如果只有一个部分,直接返回
        if len(parts) == 1:
            return parts[0]

        # 返回第一个部分作为项目根
        return parts[0]

    def discover_configurations(self, base_packages: Union[str, list[str]]) -> list[type]:
        """发现配置类

        扫描指定包中的所有配置类.

        Args:
            base_packages: 要扫描的包

        Returns:
            发现的配置类列表

        Example:
            configs = scanner.discover_configurations(['maxbot', 'miniboot'])
        """
        result = self.scan(base_packages)

        # 收集所有配置类
        config_classes = []

        # 添加@Configuration类
        for _class_name, cls in result.configurations.items():
            config_classes.append(cls)

        # 添加@MiniBootApplication类(它们也是配置类)
        for _class_name, cls in result.components.items():
            if hasattr(cls, "__is_miniboot_application__"):
                config_classes.append(cls)

        return config_classes

    def clear_all_caches(self) -> "ComponentScanner":
        """清空所有缓存"""
        self.clear_cache()
        return self

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            'cache': {
                'scan_results': len(self._scan_cache),
                'modules': len(self._module_cache),
                'classes': len(self._class_cache)
            },
            'settings': {
                'cache_enabled': self.cache_enabled,
                'lazy_loading': self.enable_lazy_loading,
                'max_cache_size': self.max_cache_size
            }
        }

        return stats

    def build_annotation_index(self) -> "ComponentScanner":
        """构建注解索引（简化实现）"""
        # 简化实现：不再需要复杂的索引功能
        return self

    def find_components_by_annotation(self, annotation_type: str) -> set[str]:
        """根据注解类型查找组件（简化实现）"""
        # 简化实现：遍历缓存的扫描结果
        components = set()
        for result in self._scan_cache.values():
            # 这里可以根据需要实现简单的查找逻辑
            # 暂时返回空集合
            pass
        return components

    def find_methods_by_annotation(self, annotation_type: str) -> set[str]:
        """根据注解类型查找方法（简化实现）"""
        # 简化实现：遍历缓存的扫描结果
        methods = set()
        for result in self._scan_cache.values():
            # 这里可以根据需要实现简单的查找逻辑
            # 暂时返回空集合
            pass
        return methods

    def _scan_package(self, package_name: str, result: ScanResult):
        """扫描单个包(优化版本)"""
        try:
            # 检查模块缓存
            if package_name in self._module_cache:
                package = self._module_cache[package_name]
            else:
                package = importlib.import_module(package_name)
                if len(self._module_cache) < self.max_cache_size:
                    self._module_cache[package_name] = package

            package_path = getattr(package, "__path__", None)

            if package_path is None:
                # 单个模块
                self._scan_module(package, result)
            else:
                # 包含子模块的包
                modules_to_scan = []

                # 收集需要扫描的模块
                for _importer, modname, _ispkg in pkgutil.walk_packages(package_path, package_name + "."):
                    if not self.scan_filter.should_include_module(modname):
                        continue
                    modules_to_scan.append(modname)

                # 批量扫描模块
                for modname in modules_to_scan:
                    try:
                        if modname in self._module_cache:
                            module = self._module_cache[modname]
                        else:
                            # 导入模块(延迟加载设置不影响实际导入)
                            module = importlib.import_module(modname)

                            if len(self._module_cache) < self.max_cache_size:
                                self._module_cache[modname] = module

                        self._scan_module(module, result)
                        result.scanned_modules.add(modname)
                    except Exception as e:
                        result.add_error(f"导入模块 {modname} 时出错: {str(e)}")

        except Exception as e:
            result.add_error(f"扫描包 {package_name} 时出错: {str(e)}")





    def _scan_module(self, module, result: ScanResult):
        """扫描单个模块"""
        # 扫描模块中的类
        for _name, obj in inspect.getmembers(module, inspect.isclass):
            # 只扫描在当前模块中定义的类
            if obj.__module__ != module.__name__:
                continue

            self._scan_class(obj, result)







    def _process_annotated_methods(self, cls: type, annotated_methods: list, result: ScanResult):
        """处理有注解的方法"""
        class_name = f"{cls.__module__}.{cls.__name__}"

        for method_name, method, annotations in annotated_methods:
            # 批量处理方法注解
            if annotations.get("__is_bean__"):
                result.add_bean_method(f"{class_name}.{method_name}", method)

            if annotations.get("__is_autowired__"):
                result.add_autowired_method(class_name, method_name)

            if annotations.get("__is_post_construct__"):
                result.add_lifecycle_method(class_name, "post_construct", method_name)

            if annotations.get("__is_pre_destroy__"):
                result.add_lifecycle_method(class_name, "pre_destroy", method_name)

            if annotations.get("__is_async__"):
                result.add_async_method(class_name, method_name)

            if annotations.get("__is_scheduled__"):
                result.add_scheduled_method(class_name, method_name)

            if annotations.get("__is_event_listener__"):
                result.add_event_listener(class_name, method_name)

    def _get_class_annotation_summary_standard(self, cls: type) -> dict[str, Any]:
        """获取类注解摘要（标准版本）"""
        annotations = {}

        # 检查各种注解
        annotations["__is_component__"] = hasattr(cls, "__is_component__") and getattr(cls, "__is_component__", False)
        annotations["__is_service__"] = hasattr(cls, "__is_service__") and getattr(cls, "__is_service__", False)
        annotations["__is_repository__"] = hasattr(cls, "__is_repository__") and getattr(cls, "__is_repository__", False)
        annotations["__is_configuration__"] = hasattr(cls, "__is_configuration__") and getattr(cls, "__is_configuration__", False)
        annotations["__is_miniboot_application__"] = hasattr(cls, "__is_miniboot_application__") and getattr(cls, "__is_miniboot_application__", False)

        # 确定类的主要类型
        class_type = None
        if annotations.get("__is_component__"):
            class_type = "component"
        elif annotations.get("__is_service__"):
            class_type = "service"
        elif annotations.get("__is_repository__"):
            class_type = "repository"
        elif annotations.get("__is_configuration__"):
            class_type = "configuration"
        elif annotations.get("__is_miniboot_application__"):
            class_type = "application"

        return {
            'class_type': class_type,
            'has_annotations': any(annotations.values()),
            'annotations': annotations,
            'module': cls.__module__,
            'name': cls.__name__
        }

    def _get_annotated_methods_standard(self, cls: type) -> list:
        """获取有注解的方法（标准版本）"""
        annotated_methods = []

        for name, method in inspect.getmembers(cls, inspect.isfunction):
            annotations = {}
            annotations["__is_bean__"] = hasattr(method, "__is_bean__") and getattr(method, "__is_bean__", False)
            annotations["__is_autowired__"] = hasattr(method, "__is_autowired__") and getattr(method, "__is_autowired__", False)
            annotations["__is_post_construct__"] = hasattr(method, "__is_post_construct__") and getattr(method, "__is_post_construct__", False)
            annotations["__is_pre_destroy__"] = hasattr(method, "__is_pre_destroy__") and getattr(method, "__is_pre_destroy__", False)
            annotations["__is_async__"] = hasattr(method, "__is_async__") and getattr(method, "__is_async__", False)
            annotations["__is_scheduled__"] = hasattr(method, "__is_scheduled__") and getattr(method, "__is_scheduled__", False)
            annotations["__is_event_listener__"] = hasattr(method, "__is_event_listener__") and getattr(method, "__is_event_listener__", False)

            if any(annotations.values()):
                annotated_methods.append((name, method, annotations))

        return annotated_methods



    def _scan_class(self, cls: type, result: ScanResult):
        """扫描单个类"""
        class_name = f"{cls.__module__}.{cls.__name__}"

        # 快速检查:如果类没有任何注解属性,跳过
        if not self._has_annotations(cls):
            return

        annotations = self._get_class_annotations(cls)

        # 应用过滤器
        if not self.scan_filter.should_include_class(cls, annotations):
            return

        # 批量检查注解(减少函数调用)
        has_component = hasattr(cls, "__is_component__") and cls.__is_component__
        has_service = hasattr(cls, "__is_service__") and cls.__is_service__
        has_repository = hasattr(cls, "__is_repository__") and cls.__is_repository__
        has_configuration = hasattr(cls, "__is_configuration__") and cls.__is_configuration__

        # 扫描组件注解
        if has_component or has_service or has_repository:
            result.add_component(class_name, cls)

        # 扫描配置注解
        if has_configuration:
            result.add_configuration(class_name, cls)

        # 扫描类中的方法和字段(只在有注解时)
        if self._has_method_annotations(cls):
            self._scan_class_methods(cls, result)

        if self._has_field_annotations(cls):
            self._scan_class_fields(cls, result)





    def _get_class_annotations(self, cls: type) -> set[str]:
        """获取类的注解集合"""
        annotations = set()

        if is_component(cls):
            annotations.add("Component")
        if is_service(cls):
            annotations.add("Service")
        if is_repository(cls):
            annotations.add("Repository")
        if is_configuration(cls):
            annotations.add("Configuration")

        return annotations

    def _has_annotations(self, cls: type) -> bool:
        """快速检查类是否有任何注解"""
        # 检查常见的注解属性
        annotation_attrs = ["__is_component__", "__is_service__", "__is_repository__", "__is_configuration__", "__is_miniboot_application__"]
        return any(hasattr(cls, attr) for attr in annotation_attrs)

    def _has_method_annotations(self, cls: type) -> bool:
        """检查类是否有方法注解"""
        for _name, method in inspect.getmembers(cls, inspect.isfunction):
            if (
                hasattr(method, "__is_bean__")
                or hasattr(method, "__is_autowired__")
                or hasattr(method, "__is_post_construct__")
                or hasattr(method, "__is_pre_destroy__")
                or hasattr(method, "__is_async__")
                or hasattr(method, "__is_scheduled__")
                or hasattr(method, "__is_event_listener__")
            ):
                return True
        return False

    def _has_field_annotations(self, cls: type) -> bool:
        """检查类是否有字段注解"""
        for name in dir(cls):
            if name.startswith("_"):
                continue
            try:
                attr = getattr(cls, name)
                if hasattr(attr, "__is_autowired__") and attr.__is_autowired__:
                    return True
            except (AttributeError, TypeError):
                continue
        return False

    def _scan_class_methods(self, cls: type, result: ScanResult):
        """扫描类中的方法"""
        class_name = f"{cls.__module__}.{cls.__name__}"

        for name, method in inspect.getmembers(cls, inspect.isfunction):
            # 批量检查注解
            annotations_found = []

            if hasattr(method, "__is_bean__") and method.__is_bean__:
                annotations_found.append(("bean", name))

            if hasattr(method, "__is_autowired__") and method.__is_autowired__:
                annotations_found.append(("autowired", name))

            if hasattr(method, "__is_post_construct__") and method.__is_post_construct__:
                annotations_found.append(("post_construct", name))

            if hasattr(method, "__is_pre_destroy__") and method.__is_pre_destroy__:
                annotations_found.append(("pre_destroy", name))

            if hasattr(method, "__is_async__") and method.__is_async__:
                annotations_found.append(("async", name))

            if hasattr(method, "__is_scheduled__") and method.__is_scheduled__:
                annotations_found.append(("scheduled", name))

            if hasattr(method, "__is_event_listener__") and method.__is_event_listener__:
                annotations_found.append(("event_listener", name))

            # 批量添加结果
            for annotation_type, method_name in annotations_found:
                if annotation_type == "bean":
                    bean_name = f"{class_name}.{method_name}"
                    result.add_bean_method(bean_name, method)
                elif annotation_type == "autowired":
                    result.add_autowired_method(class_name, method_name)
                elif annotation_type in ["post_construct", "pre_destroy"]:
                    result.add_lifecycle_method(class_name, annotation_type, method_name)
                elif annotation_type == "async":
                    result.add_async_method(class_name, method_name)
                elif annotation_type == "scheduled":
                    result.add_scheduled_method(class_name, method_name)
                elif annotation_type == "event_listener":
                    result.add_event_listener(class_name, method_name)

    def _scan_class_fields(self, cls: type, result: ScanResult):
        """扫描类中的字段"""
        class_name = f"{cls.__module__}.{cls.__name__}"

        # 批量收集字段
        autowired_fields = []

        for name in dir(cls):
            if name.startswith("_"):
                continue

            try:
                attr = getattr(cls, name)
                if hasattr(attr, "__is_autowired__") and attr.__is_autowired__:
                    autowired_fields.append(name)
            except (AttributeError, TypeError):
                continue

        # 批量添加结果
        for field_name in autowired_fields:
            result.add_autowired_field(class_name, field_name)
